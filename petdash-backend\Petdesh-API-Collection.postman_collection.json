{"info": {"_postman_id": "petdesh-backend-collection-001", "name": "Petdesh API Collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "description": "Postman collection for Petdesh backend API."}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "Signup", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\",\n  \"userType\": \"user\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/signup", "host": ["{{baseUrl}}"], "path": ["auth", "signup"]}}, "response": [{"code": 201, "body": "{\n  \"message\": \"User created successfully\"\n}"}, {"code": 400, "body": "{\n  \"message\": \"User already exists\"\n}"}]}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}, "response": [{"code": 200, "body": "{\n  \"message\": \"Login successful\",\n  \"token\": \"<jwt-token>\",\n  \"user\": { /* user object */ }\n}"}, {"code": 400, "body": "{\n  \"message\": \"User not found\"\n}"}, {"code": 401, "body": "{\n  \"message\": \"Incorrect password\"\n}"}]}, {"name": "Request Password Reset", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/request-password-reset", "host": ["{{baseUrl}}"], "path": ["auth", "request-password-reset"]}}, "response": [{"code": 200, "body": "{\n  \"message\": \"Password reset link sent to email\"\n}"}, {"code": 404, "body": "{\n  \"message\": \"User not found\"\n}"}]}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"token\": \"<reset-token>\",\n  \"newPassword\": \"newpassword123\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/reset-password", "host": ["{{baseUrl}}"], "path": ["auth", "reset-password"]}}, "response": [{"code": 200, "body": "{\n  \"message\": \"Password has been reset\"\n}"}, {"code": 400, "body": "{\n  \"message\": \"Invalid or expired token\"\n}"}]}]}, {"name": "Profile", "item": [{"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/profile/get-profile", "host": ["{{baseUrl}}"], "path": ["profile", "get-profile"]}}, "response": [{"code": 200, "body": "{\n  \"message\": \"Profile fetched successfully\",\n  \"profile\": { /* user profile object */ }\n}"}, {"code": 404, "body": "{\n  \"message\": \"User not found\"\n}"}]}, {"name": "Update Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"phoneNumber\": \"1234567890\",\n  \"streetName\": \"Main St\",\n  \"zipCode\": \"12345\",\n  \"city\": \"Metropolis\",\n  \"state\": \"CA\"\n}"}, "url": {"raw": "{{baseUrl}}/profile/create-update-profile", "host": ["{{baseUrl}}"], "path": ["profile", "create-update-profile"]}}, "response": [{"code": 200, "body": "{\n  \"message\": \"Profile updated successfully\",\n  \"profile\": { /* updated user profile object */ }\n}"}, {"code": 404, "body": "{\n  \"message\": \"User not found\"\n}"}]}]}, {"name": "Pet Profile", "item": [{"name": "Create Pet Profile", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Troy", "type": "text"}, {"key": "species", "value": "Dog", "type": "text"}, {"key": "typeOfPet", "value": "Toy Terrier", "type": "text"}, {"key": "breed", "value": "Toy terrier", "type": "text"}, {"key": "weight", "value": "47LBS", "type": "text"}, {"key": "gender", "value": "Male", "type": "text"}, {"key": "birthday", "value": "2016-02-02", "type": "text"}, {"key": "allergies", "value": "[\"<PERSON><PERSON>\",\"Soy\",\"Egg\"]", "type": "text"}, {"key": "currentMedications", "value": "", "type": "text"}, {"key": "lastVaccinatedDate", "value": "2021-10-02", "type": "text"}, {"key": "vaccinations", "value": "[{\"name\":\"Rabies\",\"date\":\"2021-10-02\"}]", "type": "text"}, {"key": "favorite<PERSON>oys", "value": "[\"Toy Rope\"]", "type": "text"}, {"key": "neutered", "value": "true", "type": "text"}, {"key": "vaccinated", "value": "true", "type": "text"}, {"key": "friendlyWithDogs", "value": "true", "type": "text"}, {"key": "friendlyWithCats", "value": "false", "type": "text"}, {"key": "friendlyWithKidsUnder10", "value": "true", "type": "text"}, {"key": "friendlyWithKidsOver10", "value": "true", "type": "text"}, {"key": "microchipped", "value": "true", "type": "text"}, {"key": "purebred", "value": "true", "type": "text"}, {"key": "pottyTrained", "value": "true", "type": "text"}, {"key": "preferredVeterinarian", "value": "", "type": "text"}, {"key": "preferredPharmacy", "value": "", "type": "text"}, {"key": "preferredGroomer", "value": "", "type": "text"}, {"key": "favoriteDogPark", "value": "", "type": "text"}, {"key": "profileImage", "type": "file"}]}, "url": {"raw": "{{baseUrl}}/api/pet/create", "host": ["{{baseUrl}}"], "path": ["api", "pet", "create"]}}, "response": [{"code": 201, "body": "{\n  \"message\": \"Pet profile created successfully\",\n  \"pet\": { /* pet object */ }\n}"}]}, {"name": "Update Pet Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "name", "value": "Troy", "type": "text"}, {"key": "weight", "value": "50LBS", "type": "text"}, {"key": "profileImage", "type": "file"}]}, "url": {"raw": "{{baseUrl}}/api/pet/update/:id", "host": ["{{baseUrl}}"], "path": ["api", "pet", "update", ":id"]}}, "response": [{"code": 200, "body": "{\n  \"message\": \"Pet profile updated successfully\",\n  \"pet\": { /* updated pet object */ }\n}"}]}, {"name": "Get Pet Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/pet/:id", "host": ["{{baseUrl}}"], "path": ["api", "pet", ":id"]}}, "response": [{"code": 200, "body": "{\n  \"message\": \"Pet profile fetched successfully\",\n  \"pet\": { /* pet object */ }\n}"}, {"code": 404, "body": "{\n  \"message\": \"Pet not found\"\n}"}]}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:5000"}, {"key": "token", "value": ""}]}